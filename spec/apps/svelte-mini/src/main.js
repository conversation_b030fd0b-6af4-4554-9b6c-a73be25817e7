import * as Sentry from "@sentry/svelte";
import App from './App.svelte'

Sentry.init({
  dsn: "http://12345@localhost/123",
  debug: true,
  integrations: [
    Sentry.browserTracingIntegration({
      // Use default tracePropagationTargets: ["localhost", /^\/$/]
      // This should automatically add headers to requests containing "localhost"
    }),
  ],
  tracesSampleRate: 1.0,
  environment: "test",
});

const app = new App({
  target: document.getElementById('app'),
})

export default app
