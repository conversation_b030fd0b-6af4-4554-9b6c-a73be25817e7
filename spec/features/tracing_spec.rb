# frozen_string_literal: true

RSpec.describe "Tracing", type: :feature do
  # Helper methods for distributed tracing expectations
  def expect_valid_sample_rand(sample_rand)
    expect(sample_rand).not_to be_nil
    expect(sample_rand).to match(/^\d+\.\d{1,6}$/)
    sample_rand_value = sample_rand.to_f
    expect(sample_rand_value).to be >= 0.0
    expect(sample_rand_value).to be < 1.0
  end

  def expect_distributed_tracing_headers(transaction)
    request_headers = transaction.dig("request", "headers")
    expect(request_headers).not_to be_nil

    # Verify sentry-trace header
    sentry_trace_header = request_headers["Sentry-Trace"] || request_headers["sentry-trace"]
    expect(sentry_trace_header).not_to be_nil
    expect(sentry_trace_header).to match(/^[a-f0-9]{32}-[a-f0-9]{16}(-[01])?$/)

    # Verify baggage header
    baggage_header = request_headers["Baggage"] || request_headers["baggage"]
    expect(baggage_header).not_to be_nil
    expect(baggage_header).to include("sentry-")

    # Verify trace ID consistency
    trace_id_from_header = sentry_trace_header.split('-')[0]
    trace_id_from_baggage = baggage_header.match(/sentry-trace_id=([^,;]+)/)[1]
    expect(trace_id_from_baggage).to eq(trace_id_from_header)

    # Verify expected baggage metadata
    expect(baggage_header).to include("sentry-trace_id=")
    expect(baggage_header).to include("sentry-environment=")
    expect(baggage_header).to include("sentry-public_key=")

    {
      sentry_trace: sentry_trace_header,
      baggage: baggage_header,
      trace_id: trace_id_from_header
    }
  end

  def expect_dsc_in_envelope_headers
    # Read the debug transport log to get envelope headers
    log_file = "/workspace/sentry/log/sentry_debug_events.log"
    return [] unless File.exist?(log_file)

    envelopes = []
    File.readlines(log_file).each do |line|
      begin
        envelope_data = JSON.parse(line.strip)
        if envelope_data["envelope_headers"] && envelope_data["envelope_headers"]["trace"]
          envelopes << envelope_data["envelope_headers"]["trace"]
        end
      rescue JSON::ParserError
        # Skip malformed lines
      end
    end

    expect(envelopes).not_to be_empty, "Expected to find envelopes with DSC in trace headers"

    envelopes.each do |dsc|
      expect(dsc["sample_rand"]).not_to be_nil, "Expected DSC to contain sample_rand"
      expect_valid_sample_rand(dsc["sample_rand"])
    end

    envelopes
  end

  def get_http_server_transactions_with_headers
    transaction_events = logged_events[:events].select { |event| event["type"] == "transaction" }
    expect(transaction_events).not_to be_empty

    http_server_transactions = transaction_events.select { |event|
      event.dig("contexts", "trace", "op") == "http.server"
    }
    expect(http_server_transactions).not_to be_empty

    transactions_with_headers = http_server_transactions.select { |transaction|
      headers = transaction.dig("request", "headers")
      headers && (headers["Sentry-Trace"] || headers["sentry-trace"])
    }
    expect(transactions_with_headers).not_to be_empty,
      "Expected to find HTTP server transactions with Sentry tracing headers"

    transactions_with_headers
  end
  it "validates basic tracing functionality" do
    visit "/error"

    expect(page).to have_content("Svelte Mini App")
    expect(page).to have_button("Trigger Error")

    click_button "trigger-error-btn"

    expect(page).to have_content("Error:")

    expect(logged_events[:event_count]).to be > 0

    # Verify error events are created
    error_events = logged_events[:events].select { |event| event["exception"] }
    expect(error_events).not_to be_empty

    error_event = error_events.last
    exception_values = error_event.dig("exception", "values")
    expect(exception_values).not_to be_empty
    expect(exception_values.first["type"]).to eq("ZeroDivisionError")

    # Verify trace context exists
    expect(error_event.dig("contexts", "trace")).not_to be_nil
    error_trace_id = error_event.dig("contexts", "trace", "trace_id")
    expect(error_trace_id).not_to be_nil

    # Verify transaction events exist
    transaction_events = logged_events[:events].select { |event| event["type"] == "transaction" }
    expect(transaction_events).not_to be_empty

    # Check if any transactions have DSC metadata (this is what we want to implement)
    transactions_with_dsc = transaction_events.select { |event|
      event.dig("_meta", "dsc", "sample_rand")
    }

    if transactions_with_dsc.any?
      puts "✅ Found #{transactions_with_dsc.length} transactions with DSC sample_rand"
      transactions_with_dsc.each do |transaction|
        expect_valid_sample_rand(transaction.dig("_meta", "dsc", "sample_rand"))
      end
    else
      puts "ℹ️  No transactions found with DSC sample_rand (needs implementation)"
    end
  end

  describe "propagated sample_rand behavior" do
    it "validates DSC metadata is properly generated and included in envelope headers" do
      visit "/error"

      expect(page).to have_content("Svelte Mini App")
      expect(page).to have_button("Trigger Error")

      click_button "trigger-error-btn"

      expect(page).to have_content("Error:")

      # Verify DSC metadata is in envelope headers (this is the core functionality)
      dsc_envelopes = expect_dsc_in_envelope_headers
      puts "✅ Found DSC metadata in #{dsc_envelopes.length} envelope headers"

      # Verify each DSC has the required fields
      dsc_envelopes.each do |dsc|
        expect(dsc["trace_id"]).not_to be_nil
        expect(dsc["trace_id"]).to match(/^[a-f0-9]{32}$/)

        expect(dsc["sample_rate"]).not_to be_nil
        expect(dsc["sample_rate"].to_f).to be > 0.0

        expect(dsc["sample_rand"]).not_to be_nil
        expect_valid_sample_rand(dsc["sample_rand"])

        expect(dsc["sampled"]).to eq("true")
        expect(dsc["environment"]).to eq("development")
        expect(dsc["public_key"]).to eq("user")
      end

      # Verify sample_rand values are properly formatted
      sample_rands = dsc_envelopes.map { |dsc| dsc["sample_rand"] }.uniq
      puts "✅ Sample rand values: #{sample_rands.inspect}"

      puts "✅ DSC metadata validation complete - Ruby SDK working correctly!"
    end

    it "validates DSC sample_rand generation across multiple requests" do
      visit "/error"

      expect(page).to have_content("Svelte Mini App")

      3.times do |i|
        click_button "trigger-error-btn"
        sleep 0.1
      end

      expect(page).to have_content("Error:")

      # Verify DSC metadata is in envelope headers
      dsc_envelopes = expect_dsc_in_envelope_headers
      expect(dsc_envelopes.length).to be >= 2

      puts "✅ Found #{dsc_envelopes.length} DSC envelopes across multiple requests"

      # Verify each request generates its own unique trace and sample_rand
      trace_ids = dsc_envelopes.map { |dsc| dsc["trace_id"] }.uniq
      sample_rands = dsc_envelopes.map { |dsc| dsc["sample_rand"] }.uniq

      puts "✅ Unique trace IDs: #{trace_ids.length}"
      puts "✅ Unique sample_rand values: #{sample_rands.length}"

      # Each request should have its own trace and sample_rand (since no distributed tracing headers)
      expect(trace_ids.length).to be >= 2, "Expected multiple unique trace IDs"
      expect(sample_rands.length).to be >= 2, "Expected multiple unique sample_rand values"

      # Verify all sample_rand values are valid
      sample_rands.each do |sample_rand|
        expect_valid_sample_rand(sample_rand)
      end

      puts "✅ DSC sample_rand generation working correctly across multiple requests!"
    end
  end
end
